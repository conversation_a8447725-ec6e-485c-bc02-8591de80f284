from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from typing import List, Optional
from datetime import datetime
from .models import UUID
from .schemas import UUIDCreate, UUIDUpdate


def create_uuid(db: Session, uuid_data: UUIDCreate, created_by: str = "admin") -> UUID:
    """Create a new UUID"""
    db_uuid = UUID(
        description=uuid_data.description,
        expires_at=uuid_data.expires_at,
        created_by=created_by
    )
    db.add(db_uuid)
    db.commit()
    db.refresh(db_uuid)
    return db_uuid


def get_uuid(db: Session, uuid: str, update_expiration: bool = True) -> Optional[UUID]:
    """
    Get a UUID by its value
    update_expiration: If True, automatically update status to 'expired' if expired
    """
    db_uuid = db.query(UUID).filter(UUID.uuid == uuid).first()

    if db_uuid and update_expiration:
        # Automatically update expiration status
        if db_uuid.update_expiration_status():
            db.commit()
            db.refresh(db_uuid)

    return db_uuid


def get_uuids(db: Session, skip: int = 0, limit: int = 100) -> List[UUID]:
    """Get all UUIDs with pagination"""
    return db.query(UUID).offset(skip).limit(limit).all()


def update_uuid(db: Session, uuid: str, uuid_update: UUIDUpdate) -> Optional[UUID]:
    """Update a UUID"""
    db_uuid = get_uuid(db, uuid)
    if not db_uuid:
        return None
    
    update_data = uuid_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_uuid, field, value)
    
    db_uuid.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_uuid)
    return db_uuid


def delete_uuid(db: Session, uuid: str) -> bool:
    """Delete a UUID"""
    db_uuid = get_uuid(db, uuid)
    if not db_uuid:
        return False
    
    db.delete(db_uuid)
    db.commit()
    return True


def validate_and_activate_uuid(db: Session, uuid: str) -> tuple[Optional[UUID], bool]:
    """
    Validate a UUID and activate it if possible via API
    Returns (uuid_object, was_activated)
    """
    db_uuid = get_uuid(db, uuid, update_expiration=True)
    if not db_uuid:
        return None, False

    # Increment usage count regardless of activation status
    db_uuid.increment_usage()

    # Try to activate if not already activated (using 'api' method)
    was_activated = False
    if db_uuid.can_be_activated():
        was_activated = db_uuid.activate(method="api")

    db.commit()
    db.refresh(db_uuid)
    return db_uuid, was_activated


def admin_activate_uuid(db: Session, uuid: str) -> tuple[Optional[UUID], bool, str]:
    """
    Manually activate a UUID via admin interface
    Returns (uuid_object, was_activated, message)
    """
    db_uuid = get_uuid(db, uuid, update_expiration=True)
    if not db_uuid:
        return None, False, "UUID not found"

    # Check if already activated
    if db_uuid.is_activated:
        return db_uuid, False, f"UUID was already activated via {db_uuid.activation_method} on {db_uuid.activated_at}"

    # Try to activate using admin method
    was_activated = db_uuid.activate(method="admin")

    if was_activated:
        db.commit()
        db.refresh(db_uuid)
        return db_uuid, True, "UUID activated successfully by admin"
    else:
        # Determine why activation failed
        if db_uuid.is_expired:
            message = "Cannot activate expired UUID"
        elif db_uuid.status != "active":
            message = f"Cannot activate UUID with status '{db_uuid.status}'"
        else:
            message = "UUID cannot be activated"

        return db_uuid, False, message


def get_uuid_stats(db: Session) -> dict:
    """Get UUID statistics"""
    total = db.query(UUID).count()
    active = db.query(UUID).filter(UUID.status == "active").count()
    inactive = db.query(UUID).filter(UUID.status == "inactive").count()
    
    # Count expired UUIDs
    now = datetime.utcnow()
    expired = db.query(UUID).filter(
        and_(
            UUID.expires_at.isnot(None),
            UUID.expires_at < now
        )
    ).count()
    
    activated = db.query(UUID).filter(UUID.is_activated == True).count()
    total_usage = db.query(func.sum(UUID.usage_count)).scalar() or 0
    
    return {
        "total_uuids": total,
        "active_uuids": active,
        "inactive_uuids": inactive,
        "expired_uuids": expired,
        "activated_uuids": activated,
        "total_usage": total_usage
    }


def search_uuids(
    db: Session,
    status: Optional[str] = None,
    is_activated: Optional[bool] = None,
    skip: int = 0,
    limit: int = 100
) -> List[UUID]:
    """Search UUIDs with filters"""
    query = db.query(UUID)

    if status:
        query = query.filter(UUID.status == status)

    if is_activated is not None:
        query = query.filter(UUID.is_activated == is_activated)

    return query.offset(skip).limit(limit).all()


def update_expired_uuids(db: Session) -> int:
    """
    Bulk update all expired UUIDs to have status 'expired'
    Returns the number of UUIDs updated
    """
    now = datetime.utcnow()

    # Find all UUIDs that are expired but not marked as expired
    expired_uuids = db.query(UUID).filter(
        and_(
            UUID.expires_at.isnot(None),
            UUID.expires_at < now,
            UUID.status != "expired"
        )
    ).all()

    count = 0
    for uuid_obj in expired_uuids:
        if uuid_obj.update_expiration_status():
            count += 1

    if count > 0:
        db.commit()

    return count


def get_uuids_with_expiration_check(db: Session, skip: int = 0, limit: int = 100) -> List[UUID]:
    """Get UUIDs with automatic expiration status updates"""
    # First, update any expired UUIDs
    update_expired_uuids(db)

    # Then return the requested UUIDs
    return get_uuids(db, skip=skip, limit=limit)
