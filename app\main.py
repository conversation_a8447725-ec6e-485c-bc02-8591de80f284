from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException, status
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session
import os
from .database import create_tables, get_db
from .routers import admin, public
from .config import settings
from .crud import get_uuid_stats, get_uuids_with_expiration_check
from .dependencies import get_current_admin_user
from .schemas import User
from .background_tasks import start_background_tasks, stop_background_tasks

# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="A comprehensive UUID management system with admin interface"
)

# Create database tables
create_tables()

# Mount static files
if not os.path.exists("static"):
    os.makedirs("static")
    os.makedirs("static/css")
    os.makedirs("static/js")

app.mount("/static", StaticFiles(directory="static"), name="static")

# Setup templates
templates = Jinja2Templates(directory="templates")

# Include routers
app.include_router(admin.router)
app.include_router(public.router)

# Security
security = HTTPBearer(auto_error=False)


@app.on_event("startup")
async def startup_event():
    """Start background tasks on application startup"""
    await start_background_tasks()


@app.on_event("shutdown")
async def shutdown_event():
    """Stop background tasks on application shutdown"""
    await stop_background_tasks()


@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """Root endpoint - redirect to login"""
    return RedirectResponse(url="/login")


@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    """Login page"""
    return templates.TemplateResponse("login.html", {"request": request})


@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(
    request: Request,
    db: Session = Depends(get_db)
):
    """Admin dashboard"""
    # For web interface, we'll handle auth via JavaScript
    # This is a simplified approach for demo purposes
    stats = get_uuid_stats(db)
    recent_uuids = get_uuids_with_expiration_check(db, skip=0, limit=10)

    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "user": {"username": "admin"},  # Simplified for demo
        "stats": stats,
        "recent_uuids": recent_uuids
    })


@app.get("/manage-uuids", response_class=HTMLResponse)
async def manage_uuids_page(
    request: Request,
    db: Session = Depends(get_db)
):
    """UUID management page"""
    # For web interface, we'll handle auth via JavaScript
    # This is a simplified approach for demo purposes
    uuids = get_uuids_with_expiration_check(db, skip=0, limit=1000)  # Adjust limit as needed

    return templates.TemplateResponse("uuid_management.html", {
        "request": request,
        "user": {"username": "admin"},  # Simplified for demo
        "uuids": uuids
    })


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "app": settings.app_name, "version": settings.app_version}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
