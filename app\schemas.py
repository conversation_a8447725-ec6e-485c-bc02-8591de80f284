from pydantic import BaseModel, Field, ConfigDict
from typing import Optional
from datetime import datetime


class UUIDBase(BaseModel):
    description: Optional[str] = None
    expires_at: Optional[datetime] = None


class UUIDCreate(UUIDBase):
    """Schema for creating a new UUID"""
    pass


class UUIDUpdate(BaseModel):
    """Schema for updating an existing UUID"""
    description: Optional[str] = None
    expires_at: Optional[datetime] = None
    status: Optional[str] = Field(None, pattern="^(active|inactive)$")


class UUIDResponse(UUIDBase):
    """Schema for UUID response"""
    uuid: str
    created_at: datetime
    updated_at: Optional[datetime]
    last_used_at: Optional[datetime]
    activated_at: Optional[datetime]
    status: str
    effective_status: str
    usage_count: int
    is_activated: bool
    is_expired: bool
    is_valid: bool
    activation_method: Optional[str]
    created_by: str

    model_config = ConfigDict(from_attributes=True)


class UUIDValidationResponse(BaseModel):
    """Schema for UUID validation response"""
    uuid: str
    is_valid: bool
    is_activated: bool
    is_expired: bool
    status: str
    effective_status: str
    message: str
    activated_now: bool = False  # True if UUID was just activated
    activation_method: Optional[str] = None


class UUIDStatusResponse(BaseModel):
    """Schema for UUID status check response"""
    uuid: str
    status: str
    effective_status: str
    is_valid: bool
    is_activated: bool
    is_expired: bool
    usage_count: int
    created_at: datetime
    last_used_at: Optional[datetime]
    activated_at: Optional[datetime]
    expires_at: Optional[datetime]
    activation_method: Optional[str]


class Token(BaseModel):
    """Schema for authentication token"""
    access_token: str
    token_type: str


class TokenData(BaseModel):
    """Schema for token data"""
    username: Optional[str] = None


class User(BaseModel):
    """Schema for user"""
    username: str
    is_admin: bool = True


class UserInDB(User):
    """Schema for user in database"""
    hashed_password: str


class LoginRequest(BaseModel):
    """Schema for login request"""
    username: str
    password: str


class UUIDListResponse(BaseModel):
    """Schema for UUID list response"""
    total: int
    uuids: list[UUIDResponse]


class UUIDStatsResponse(BaseModel):
    """Schema for UUID statistics response"""
    total_uuids: int
    active_uuids: int
    inactive_uuids: int
    expired_uuids: int
    activated_uuids: int
    total_usage: int


class UUIDActivationRequest(BaseModel):
    """Schema for admin UUID activation request"""
    pass  # No additional data needed, just the UUID from the path


class UUIDActivationResponse(BaseModel):
    """Schema for UUID activation response"""
    uuid: str
    success: bool
    message: str
    was_already_activated: bool
    activation_method: Optional[str]
    activated_at: Optional[datetime]
