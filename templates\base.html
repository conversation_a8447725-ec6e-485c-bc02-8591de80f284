<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}UUID Management System{% endblock %}</title>

    <!-- Apple Design System CSS -->
    <link href="/static/css/apple-design-system.css" rel="stylesheet">

    <!-- SF Symbols and Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if user %}
    <!-- Apple-style Sidebar -->
    <nav class="sidebar-apple" id="sidebar">
        <div class="sidebar-apple-header">
            <a href="/dashboard" class="sidebar-apple-brand">
                <i class="fas fa-key"></i>
                UUID Manager
            </a>
        </div>
        <div class="sidebar-apple-nav">
            <div class="sidebar-apple-nav-item">
                <a href="/dashboard" class="sidebar-apple-nav-link" data-page="dashboard">
                    <i class="fas fa-chart-line"></i>
                    Dashboard
                </a>
            </div>
            <div class="sidebar-apple-nav-item">
                <a href="/manage-uuids" class="sidebar-apple-nav-link" data-page="manage">
                    <i class="fas fa-list-ul"></i>
                    Manage UUIDs
                </a>
            </div>
            <div class="sidebar-apple-nav-item" style="margin-top: var(--spacing-xl);">
                <a href="#" class="sidebar-apple-nav-link" onclick="logout()" style="color: var(--color-danger);">
                    <i class="fas fa-arrow-right-from-bracket"></i>
                    Sign Out
                </a>
            </div>
        </div>
    </nav>

    <!-- Mobile Navigation Toggle -->
    <button class="btn-apple btn-apple-ghost" id="mobileMenuToggle" style="position: fixed; top: var(--spacing-md); left: var(--spacing-md); z-index: 1100; display: none;">
        <i class="fas fa-bars"></i>
    </button>
    {% endif %}

    <!-- Main content -->
    <div class="main-content-apple" id="mainContent">
        <div class="container-apple" style="padding-top: var(--spacing-xl); padding-bottom: var(--spacing-xl);">
            {% block content %}{% endblock %}
        </div>
    </div>
    <!-- Scripts -->
    <script>
        // Global functions
        function logout() {
            localStorage.removeItem('access_token');
            window.location.href = '/login';
        }

        function getAuthHeaders() {
            const token = localStorage.getItem('access_token');
            return token ? { 'Authorization': `Bearer ${token}` } : {};
        }

        function checkAuth() {
            const token = localStorage.getItem('access_token');
            if (!token && window.location.pathname !== '/login') {
                window.location.href = '/login';
            }
        }

        // Mobile navigation toggle
        function toggleMobileNav() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            sidebar.classList.toggle('show');
        }

        // Set active navigation item
        function setActiveNavItem() {
            const currentPage = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar-apple-nav-link');

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === currentPage) {
                    link.classList.add('active');
                }
            });
        }

        // Responsive navigation handling
        function handleResponsiveNav() {
            const mobileToggle = document.getElementById('mobileMenuToggle');
            const sidebar = document.getElementById('sidebar');

            if (window.innerWidth <= 768) {
                if (mobileToggle) mobileToggle.style.display = 'flex';
                if (sidebar) sidebar.classList.remove('show');
            } else {
                if (mobileToggle) mobileToggle.style.display = 'none';
                if (sidebar) sidebar.classList.remove('show');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            setActiveNavItem();
            handleResponsiveNav();

            // Add mobile toggle event listener
            const mobileToggle = document.getElementById('mobileMenuToggle');
            if (mobileToggle) {
                mobileToggle.addEventListener('click', toggleMobileNav);
            }

            // Handle window resize
            window.addEventListener('resize', handleResponsiveNav);

            // Close mobile nav when clicking outside
            document.addEventListener('click', function(e) {
                const sidebar = document.getElementById('sidebar');
                const mobileToggle = document.getElementById('mobileMenuToggle');

                if (window.innerWidth <= 768 &&
                    sidebar && sidebar.classList.contains('show') &&
                    !sidebar.contains(e.target) &&
                    !mobileToggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            });
        });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>
