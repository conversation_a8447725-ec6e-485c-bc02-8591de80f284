{% extends "base.html" %}

{% block title %}Dashboard - UUID Management System{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: var(--spacing-md);">
        <div>
            <h1 class="page-title">Dashboard</h1>
            <p class="page-subtitle">Overview of your UUID management system</p>
        </div>
        <div class="badge-apple badge-apple-primary">
            <i class="fas fa-user"></i>
            Welcome, {{ user.username }}
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-2xl);">
    <div class="card-apple" style="background: linear-gradient(135deg, var(--color-primary) 0%, #0056CC 100%); color: var(--color-white); border: none;">
        <div class="card-apple-body">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3 style="font-size: var(--font-size-3xl); font-weight: var(--font-weight-bold); margin: 0; color: var(--color-white);">{{ stats.total_uuids }}</h3>
                    <p style="margin: var(--spacing-xs) 0 0 0; color: rgba(255, 255, 255, 0.8); font-weight: var(--font-weight-medium);">Total UUIDs</p>
                </div>
                <div style="opacity: 0.7;">
                    <i class="fas fa-key" style="font-size: 2.5rem;"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="card-apple" style="background: linear-gradient(135deg, var(--color-success) 0%, #28A745 100%); color: var(--color-white); border: none;">
        <div class="card-apple-body">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3 style="font-size: var(--font-size-3xl); font-weight: var(--font-weight-bold); margin: 0; color: var(--color-white);">{{ stats.active_uuids }}</h3>
                    <p style="margin: var(--spacing-xs) 0 0 0; color: rgba(255, 255, 255, 0.8); font-weight: var(--font-weight-medium);">Active UUIDs</p>
                </div>
                <div style="opacity: 0.7;">
                    <i class="fas fa-check-circle" style="font-size: 2.5rem;"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="card-apple" style="background: linear-gradient(135deg, var(--color-orange) 0%, #E67E22 100%); color: var(--color-white); border: none;">
        <div class="card-apple-body">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3 style="font-size: var(--font-size-3xl); font-weight: var(--font-weight-bold); margin: 0; color: var(--color-white);">{{ stats.activated_uuids }}</h3>
                    <p style="margin: var(--spacing-xs) 0 0 0; color: rgba(255, 255, 255, 0.8); font-weight: var(--font-weight-medium);">Activated UUIDs</p>
                </div>
                <div style="opacity: 0.7;">
                    <i class="fas fa-star" style="font-size: 2.5rem;"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="card-apple" style="background: linear-gradient(135deg, var(--color-purple) 0%, #8E44AD 100%); color: var(--color-white); border: none;">
        <div class="card-apple-body">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3 style="font-size: var(--font-size-3xl); font-weight: var(--font-weight-bold); margin: 0; color: var(--color-white);">{{ stats.total_usage }}</h3>
                    <p style="margin: var(--spacing-xs) 0 0 0; color: rgba(255, 255, 255, 0.8); font-weight: var(--font-weight-medium);">Total Usage</p>
                </div>
                <div style="opacity: 0.7;">
                    <i class="fas fa-chart-line" style="font-size: 2.5rem;"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="card-apple" style="margin-bottom: var(--spacing-2xl);">
    <div class="card-apple-header">
        <h3 style="margin: 0; display: flex; align-items: center; gap: var(--spacing-sm);">
            <i class="fas fa-bolt" style="color: var(--color-primary);"></i>
            Quick Actions
        </h3>
    </div>
    <div class="card-apple-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-md);">
            <button class="btn-apple btn-apple-primary btn-apple-lg" onclick="createNewUUID()">
                <i class="fas fa-plus"></i>
                Create New UUID
            </button>
            <a href="/manage-uuids" class="btn-apple btn-apple-outline btn-apple-lg" style="text-decoration: none;">
                <i class="fas fa-list-ul"></i>
                Manage All UUIDs
            </a>
        </div>
    </div>
</div>

<!-- Recent UUIDs -->
<div class="card-apple">
    <div class="card-apple-header">
        <h3 style="margin: 0; display: flex; align-items: center; gap: var(--spacing-sm);">
            <i class="fas fa-clock" style="color: var(--color-primary);"></i>
            Recent UUIDs
        </h3>
    </div>
    <div class="card-apple-body" style="padding: 0;">
        {% if recent_uuids %}
        <div class="table-apple">
            <thead>
                <tr>
                    <th>UUID</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Usage</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for uuid in recent_uuids %}
                <tr>
                    <td>
                        <code style="background: var(--color-gray-100); padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-family: var(--font-family-mono); color: var(--color-primary);">
                            {{ uuid.uuid[:8] }}...
                        </code>
                    </td>
                    <td>
                        <div style="display: flex; gap: var(--spacing-xs); flex-wrap: wrap;">
                            <span class="badge-apple badge-apple-{{ 'success' if uuid.status == 'active' else 'secondary' }}">
                                {{ uuid.status }}
                            </span>
                            {% if uuid.is_activated %}
                            <span class="badge-apple badge-apple-warning">
                                <i class="fas fa-star"></i>
                                Activated
                            </span>
                            {% endif %}
                        </div>
                    </td>
                    <td style="color: var(--color-text-secondary); font-size: var(--font-size-sm);">
                        {{ uuid.created_at.strftime('%Y-%m-%d %H:%M') }}
                    </td>
                    <td>
                        <span class="badge-apple badge-apple-secondary">{{ uuid.usage_count }}</span>
                    </td>
                    <td>
                        <button class="btn-apple btn-apple-ghost btn-apple-sm" onclick="viewUUID('{{ uuid.uuid }}')" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </div>
        {% else %}
        <div style="text-align: center; padding: var(--spacing-3xl); color: var(--color-text-secondary);">
            <div style="font-size: 3rem; margin-bottom: var(--spacing-lg); opacity: 0.5;">
                <i class="fas fa-inbox"></i>
            </div>
            <h4 style="color: var(--color-text-secondary); margin-bottom: var(--spacing-sm);">No UUIDs Yet</h4>
            <p style="margin-bottom: var(--spacing-lg);">Get started by creating your first UUID</p>
            <button class="btn-apple btn-apple-primary" onclick="createNewUUID()">
                <i class="fas fa-plus"></i>
                Create First UUID
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- Create UUID Modal -->
<div class="modal-apple" id="createUUIDModal">
    <div class="modal-apple-content">
        <div class="modal-apple-header">
            <h3 class="modal-apple-title">Create New UUID</h3>
            <button type="button" class="modal-apple-close" onclick="closeCreateModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-apple-body">
            <form id="createUUIDForm">
                <div class="form-group-apple">
                    <label for="description" class="form-label-apple">Description (Optional)</label>
                    <textarea
                        class="form-input-apple"
                        id="description"
                        name="description"
                        rows="3"
                        placeholder="Enter a description for this UUID..."
                        style="resize: vertical; min-height: 80px;"
                    ></textarea>
                </div>
                <div class="form-group-apple">
                    <label for="expires_at" class="form-label-apple">Expiration Date (Optional)</label>
                    <input
                        type="datetime-local"
                        class="form-input-apple"
                        id="expires_at"
                        name="expires_at"
                    >
                </div>
            </form>
        </div>
        <div class="modal-apple-footer">
            <button type="button" class="btn-apple btn-apple-secondary" onclick="closeCreateModal()">
                Cancel
            </button>
            <button type="button" class="btn-apple btn-apple-primary" onclick="submitCreateUUID()" id="createUUIDButton">
                <i class="fas fa-plus"></i>
                Create UUID
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function createNewUUID() {
    const modal = document.getElementById('createUUIDModal');
    modal.classList.add('show');
    document.body.style.overflow = 'hidden';
}

function closeCreateModal() {
    const modal = document.getElementById('createUUIDModal');
    modal.classList.remove('show');
    document.body.style.overflow = '';

    // Reset form
    document.getElementById('createUUIDForm').reset();
}

async function submitCreateUUID() {
    const form = document.getElementById('createUUIDForm');
    const formData = new FormData(form);
    const button = document.getElementById('createUUIDButton');

    const data = {
        description: formData.get('description') || null,
        expires_at: formData.get('expires_at') || null
    };

    // Show loading state
    const originalContent = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<div class="loading-apple"></div> Creating...';

    try {
        const response = await fetch('/admin/uuids', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...getAuthHeaders()
            },
            body: JSON.stringify(data)
        });

        if (response.ok) {
            const result = await response.json();

            // Show success state
            button.innerHTML = '<i class="fas fa-check"></i> Created!';
            button.style.background = 'var(--color-success)';

            // Show success notification
            showNotification(`UUID created successfully: ${result.uuid.substring(0, 8)}...`, 'success');

            // Close modal and reload after delay
            setTimeout(() => {
                closeCreateModal();
                location.reload();
            }, 1000);
        } else {
            const error = await response.json();
            showNotification(`Error: ${error.detail}`, 'error');

            // Reset button
            button.disabled = false;
            button.innerHTML = originalContent;
        }
    } catch (error) {
        showNotification(`Error: ${error.message}`, 'error');

        // Reset button
        button.disabled = false;
        button.innerHTML = originalContent;
    }
}

function viewUUID(uuid) {
    window.location.href = `/manage-uuids#${uuid}`;
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: var(--spacing-lg);
        right: var(--spacing-lg);
        background: ${type === 'success' ? 'var(--color-success)' : type === 'error' ? 'var(--color-danger)' : 'var(--color-primary)'};
        color: var(--color-white);
        padding: var(--spacing-md) var(--spacing-lg);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform var(--transition-base);
        max-width: 400px;
        font-weight: var(--font-weight-medium);
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after delay
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Close modal when clicking outside
document.addEventListener('click', function(e) {
    const modal = document.getElementById('createUUIDModal');
    if (e.target === modal) {
        closeCreateModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeCreateModal();
    }
});
</script>
{% endblock %}
