{% extends "base.html" %}

{% block title %}Dashboard - UUID Management System{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: var(--spacing-md);">
        <div>
            <h1 class="page-title">Dashboard</h1>
            <p class="page-subtitle">Overview of your UUID management system</p>
        </div>
        <div class="badge-apple badge-apple-primary">
            <i class="fas fa-user"></i>
            Welcome, {{ user.username }}
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-2xl);">
    <div class="card-apple" style="background: linear-gradient(135deg, var(--color-primary) 0%, #0056CC 100%); color: var(--color-white); border: none;">
        <div class="card-apple-body">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3 style="font-size: var(--font-size-3xl); font-weight: var(--font-weight-bold); margin: 0; color: var(--color-white);">{{ stats.total_uuids }}</h3>
                    <p style="margin: var(--spacing-xs) 0 0 0; color: rgba(255, 255, 255, 0.8); font-weight: var(--font-weight-medium);">Total UUIDs</p>
                </div>
                <div style="opacity: 0.7;">
                    <i class="fas fa-key" style="font-size: 2.5rem;"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="card-apple" style="background: linear-gradient(135deg, var(--color-success) 0%, #28A745 100%); color: var(--color-white); border: none;">
        <div class="card-apple-body">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3 style="font-size: var(--font-size-3xl); font-weight: var(--font-weight-bold); margin: 0; color: var(--color-white);">{{ stats.active_uuids }}</h3>
                    <p style="margin: var(--spacing-xs) 0 0 0; color: rgba(255, 255, 255, 0.8); font-weight: var(--font-weight-medium);">Active UUIDs</p>
                </div>
                <div style="opacity: 0.7;">
                    <i class="fas fa-check-circle" style="font-size: 2.5rem;"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="card-apple" style="background: linear-gradient(135deg, var(--color-orange) 0%, #E67E22 100%); color: var(--color-white); border: none;">
        <div class="card-apple-body">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3 style="font-size: var(--font-size-3xl); font-weight: var(--font-weight-bold); margin: 0; color: var(--color-white);">{{ stats.activated_uuids }}</h3>
                    <p style="margin: var(--spacing-xs) 0 0 0; color: rgba(255, 255, 255, 0.8); font-weight: var(--font-weight-medium);">Activated UUIDs</p>
                </div>
                <div style="opacity: 0.7;">
                    <i class="fas fa-star" style="font-size: 2.5rem;"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="card-apple" style="background: linear-gradient(135deg, var(--color-purple) 0%, #8E44AD 100%); color: var(--color-white); border: none;">
        <div class="card-apple-body">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3 style="font-size: var(--font-size-3xl); font-weight: var(--font-weight-bold); margin: 0; color: var(--color-white);">{{ stats.total_usage }}</h3>
                    <p style="margin: var(--spacing-xs) 0 0 0; color: rgba(255, 255, 255, 0.8); font-weight: var(--font-weight-medium);">Total Usage</p>
                </div>
                <div style="opacity: 0.7;">
                    <i class="fas fa-chart-line" style="font-size: 2.5rem;"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="card-apple" style="margin-bottom: var(--spacing-2xl);">
    <div class="card-apple-header">
        <h3 style="margin: 0; display: flex; align-items: center; gap: var(--spacing-sm);">
            <i class="fas fa-bolt" style="color: var(--color-primary);"></i>
            Quick Actions
        </h3>
    </div>
    <div class="card-apple-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-md);">
            <button class="btn-apple btn-apple-primary btn-apple-lg" onclick="createNewUUID()">
                <i class="fas fa-plus"></i>
                Create New UUID
            </button>
            <a href="/manage-uuids" class="btn-apple btn-apple-outline btn-apple-lg" style="text-decoration: none;">
                <i class="fas fa-list-ul"></i>
                Manage All UUIDs
            </a>
        </div>
    </div>
</div>

<!-- Recent UUIDs -->
<div class="card-apple">
    <div class="card-apple-header">
        <h3 style="margin: 0; display: flex; align-items: center; gap: var(--spacing-sm);">
            <i class="fas fa-clock" style="color: var(--color-primary);"></i>
            Recent UUIDs
        </h3>
        <a href="/manage-uuids" class="btn-apple btn-apple-ghost btn-apple-sm" style="text-decoration: none;">
            <i class="fas fa-arrow-right"></i>
            View All
        </a>
    </div>
    <div class="card-apple-body">
        {% if recent_uuids %}
        <div id="recentUuidsContainer" class="fade-in-up">
            {% for uuid in recent_uuids %}
            <div class="recent-uuid-card">
                <div class="recent-uuid-header">
                    <div style="flex: 1; min-width: 0;">
                        <!-- UUID Display -->
                        <div class="uuid-display" onclick="toggleUUIDExpansion(this)" title="Click to expand/collapse" style="max-width: 180px;">
                            <span class="uuid-text">{{ uuid.uuid }}</span>
                            <i class="fas fa-chevron-down expand-icon"></i>
                            <i class="fas fa-copy copy-icon" onclick="copyUUID(event, '{{ uuid.uuid }}')" title="Copy UUID"></i>
                        </div>

                        {% if uuid.description %}
                        <div style="margin-top: var(--spacing-xs); color: var(--color-text-secondary); font-size: var(--font-size-sm); font-weight: var(--font-weight-medium);">
                            {{ uuid.description }}
                        </div>
                        {% endif %}
                    </div>

                    <!-- Status Badges -->
                    <div class="status-badge-group">
                        <span class="status-badge-{{ 'success' if uuid.status == 'active' else 'danger' if uuid.status == 'expired' else 'secondary' }}">
                            <i class="fas fa-{{ 'check-circle' if uuid.status == 'active' else 'times-circle' if uuid.status == 'expired' else 'pause-circle' }}"></i>
                            {{ uuid.status.title() }}
                        </span>

                        {% if uuid.is_activated %}
                        <span class="status-badge-warning">
                            <i class="fas fa-star"></i>
                            Activated
                        </span>
                        {% endif %}
                    </div>
                </div>

                <div class="recent-uuid-footer">
                    <div style="display: flex; gap: var(--spacing-lg); flex-wrap: wrap;">
                        <div style="display: flex; flex-direction: column; gap: var(--spacing-xs);">
                            <span style="font-size: var(--font-size-xs); color: var(--color-text-tertiary); font-weight: var(--font-weight-medium); text-transform: uppercase; letter-spacing: 0.5px;">Created</span>
                            <span style="font-size: var(--font-size-sm); color: var(--color-text-secondary); font-weight: var(--font-weight-medium);">
                                {{ uuid.created_at.strftime('%Y-%m-%d %H:%M') }}
                            </span>
                        </div>

                        <div style="display: flex; flex-direction: column; gap: var(--spacing-xs);">
                            <span style="font-size: var(--font-size-xs); color: var(--color-text-tertiary); font-weight: var(--font-weight-medium); text-transform: uppercase; letter-spacing: 0.5px;">Usage</span>
                            <span class="status-badge-secondary">{{ uuid.usage_count }}</span>
                        </div>

                        {% if uuid.last_used_at %}
                        <div style="display: flex; flex-direction: column; gap: var(--spacing-xs);">
                            <span style="font-size: var(--font-size-xs); color: var(--color-text-tertiary); font-weight: var(--font-weight-medium); text-transform: uppercase; letter-spacing: 0.5px;">Last Used</span>
                            <span style="font-size: var(--font-size-sm); color: var(--color-text-secondary); font-weight: var(--font-weight-medium);">
                                {{ uuid.last_used_at.strftime('%m-%d %H:%M') }}
                            </span>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Quick Actions -->
                    <div class="action-button-group">
                        <button class="action-btn" onclick="viewUUID('{{ uuid.uuid }}')" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn primary" onclick="testUUID('{{ uuid.uuid }}')" title="Test UUID">
                            <i class="fas fa-vial"></i>
                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="empty-state" style="padding: var(--spacing-xl);">
            <div class="empty-state-icon" style="font-size: 2.5rem; margin-bottom: var(--spacing-md);">
                <i class="fas fa-clock"></i>
            </div>
            <h4 class="empty-state-title" style="font-size: var(--font-size-lg); margin-bottom: var(--spacing-sm);">No Recent Activity</h4>
            <p class="empty-state-description" style="margin-bottom: var(--spacing-lg); font-size: var(--font-size-sm);">
                Create or use UUIDs to see recent activity here
            </p>
            <a href="/manage-uuids" class="btn-apple btn-apple-primary" style="text-decoration: none;">
                <i class="fas fa-plus"></i>
                Create UUID
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Create UUID Modal -->
<div class="modal-apple" id="createUUIDModal">
    <div class="modal-apple-content">
        <div class="modal-apple-header">
            <h3 class="modal-apple-title">Create New UUID</h3>
            <button type="button" class="modal-apple-close" onclick="closeCreateModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-apple-body">
            <form id="createUUIDForm">
                <div class="form-group-apple">
                    <label for="description" class="form-label-apple">Description (Optional)</label>
                    <textarea
                        class="form-input-apple"
                        id="description"
                        name="description"
                        rows="3"
                        placeholder="Enter a description for this UUID..."
                        style="resize: vertical; min-height: 80px;"
                    ></textarea>
                </div>
                <div class="form-group-apple">
                    <label for="expires_at" class="form-label-apple">Expiration Date (Optional)</label>
                    <input
                        type="datetime-local"
                        class="form-input-apple"
                        id="expires_at"
                        name="expires_at"
                    >
                </div>
            </form>
        </div>
        <div class="modal-apple-footer">
            <button type="button" class="btn-apple btn-apple-secondary" onclick="closeCreateModal()">
                Cancel
            </button>
            <button type="button" class="btn-apple btn-apple-primary" onclick="submitCreateUUID()" id="createUUIDButton">
                <i class="fas fa-plus"></i>
                Create UUID
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function createNewUUID() {
    const modal = document.getElementById('createUUIDModal');
    modal.classList.add('show');
    document.body.style.overflow = 'hidden';
}

function closeCreateModal() {
    const modal = document.getElementById('createUUIDModal');
    modal.classList.remove('show');
    document.body.style.overflow = '';

    // Reset form
    document.getElementById('createUUIDForm').reset();
}

async function submitCreateUUID() {
    const form = document.getElementById('createUUIDForm');
    const formData = new FormData(form);
    const button = document.getElementById('createUUIDButton');

    const data = {
        description: formData.get('description') || null,
        expires_at: formData.get('expires_at') || null
    };

    // Show loading state
    const originalContent = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<div class="loading-apple"></div> Creating...';

    try {
        const response = await fetch('/admin/uuids', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...getAuthHeaders()
            },
            body: JSON.stringify(data)
        });

        if (response.ok) {
            const result = await response.json();

            // Show success state
            button.innerHTML = '<i class="fas fa-check"></i> Created!';
            button.style.background = 'var(--color-success)';

            // Show success notification
            showNotification(`UUID created successfully: ${result.uuid.substring(0, 8)}...`, 'success');

            // Close modal and reload after delay
            setTimeout(() => {
                closeCreateModal();
                location.reload();
            }, 1000);
        } else {
            const error = await response.json();
            showNotification(`Error: ${error.detail}`, 'error');

            // Reset button
            button.disabled = false;
            button.innerHTML = originalContent;
        }
    } catch (error) {
        showNotification(`Error: ${error.message}`, 'error');

        // Reset button
        button.disabled = false;
        button.innerHTML = originalContent;
    }
}

// Enhanced UUID Display Functions
function toggleUUIDExpansion(element) {
    element.classList.toggle('expanded');

    // Add animation class
    element.classList.add('fade-in-up');
    setTimeout(() => {
        element.classList.remove('fade-in-up');
    }, 400);
}

function copyUUID(event, uuid) {
    event.stopPropagation(); // Prevent expansion toggle

    navigator.clipboard.writeText(uuid).then(() => {
        const copyIcon = event.target;
        const originalClass = copyIcon.className;

        // Show success feedback
        copyIcon.className = 'fas fa-check';
        copyIcon.style.color = 'var(--color-success)';

        // Add success animation to parent
        const uuidDisplay = copyIcon.closest('.uuid-display');
        uuidDisplay.classList.add('uuid-copy-success');

        // Show notification
        showNotification(`UUID copied to clipboard: ${uuid.substring(0, 8)}...`, 'success');

        // Reset after delay
        setTimeout(() => {
            copyIcon.className = originalClass;
            copyIcon.style.color = '';
            uuidDisplay.classList.remove('uuid-copy-success');
        }, 1000);
    }).catch(err => {
        showNotification('Failed to copy UUID', 'error');
        console.error('Failed to copy: ', err);
    });
}

function viewUUID(uuid) {
    window.location.href = `/manage-uuids#${uuid}`;
}

async function testUUID(uuid) {
    const button = event.target.closest('.action-btn');
    const originalContent = button.innerHTML;

    // Show loading state
    button.disabled = true;
    button.innerHTML = '<div class="loading-spinner"></div>';

    try {
        const response = await fetch(`/validate/${uuid}`, {
            method: 'GET',
            headers: getAuthHeaders()
        });

        if (response.ok) {
            const result = await response.json();

            // Show success state
            button.innerHTML = '<i class="fas fa-check"></i>';
            button.style.background = 'var(--color-success)';
            button.style.color = 'var(--color-white)';

            showNotification(`UUID test successful: ${result.message}`, 'success');

            // Reset button after delay
            setTimeout(() => {
                button.disabled = false;
                button.innerHTML = originalContent;
                button.style.background = '';
                button.style.color = '';
            }, 2000);
        } else {
            const error = await response.json();

            // Show error state
            button.innerHTML = '<i class="fas fa-times"></i>';
            button.style.background = 'var(--color-danger)';
            button.style.color = 'var(--color-white)';

            showNotification(`UUID test failed: ${error.detail}`, 'error');

            // Reset button after delay
            setTimeout(() => {
                button.disabled = false;
                button.innerHTML = originalContent;
                button.style.background = '';
                button.style.color = '';
            }, 2000);
        }
    } catch (error) {
        showNotification(`Test error: ${error.message}`, 'error');

        // Reset button
        button.disabled = false;
        button.innerHTML = originalContent;
    }
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: var(--spacing-lg);
        right: var(--spacing-lg);
        background: ${type === 'success' ? 'var(--color-success)' : type === 'error' ? 'var(--color-danger)' : 'var(--color-primary)'};
        color: var(--color-white);
        padding: var(--spacing-md) var(--spacing-lg);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform var(--transition-base);
        max-width: 400px;
        font-weight: var(--font-weight-medium);
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after delay
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

function getAuthHeaders() {
    const token = localStorage.getItem('access_token');
    return token ? {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    } : {
        'Content-Type': 'application/json'
    };
}

// Close modal when clicking outside
document.addEventListener('click', function(e) {
    const modal = document.getElementById('createUUIDModal');
    if (e.target === modal) {
        closeCreateModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeCreateModal();
    }
});

// Initialize animations on page load
document.addEventListener('DOMContentLoaded', function() {
    // Add staggered animation to recent UUID cards
    const cards = document.querySelectorAll('.recent-uuid-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in-up');
    });
});
</script>
{% endblock %}
