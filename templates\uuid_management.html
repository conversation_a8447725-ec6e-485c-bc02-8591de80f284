{% extends "base.html" %}

{% block title %}Manage UUIDs - UUID Management System{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: var(--spacing-md);">
        <div>
            <h1 class="page-title">Manage UUIDs</h1>
            <p class="page-subtitle">Create, edit, and monitor your UUID collection</p>
        </div>
        <button class="btn-apple btn-apple-primary btn-apple-lg" onclick="createNewUUID()">
            <i class="fas fa-plus"></i>
            Create New UUID
        </button>
    </div>
</div>

<!-- Filters -->
<div class="card-apple" style="margin-bottom: var(--spacing-2xl);">
    <div class="card-apple-header">
        <h3 style="margin: 0; display: flex; align-items: center; gap: var(--spacing-sm);">
            <i class="fas fa-filter" style="color: var(--color-primary);"></i>
            Filters
        </h3>
    </div>
    <div class="card-apple-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-md); align-items: end;">
            <div class="form-group-apple" style="margin-bottom: 0;">
                <label for="statusFilter" class="form-label-apple">Status</label>
                <select class="form-input-apple" id="statusFilter" onchange="filterUUIDs()">
                    <option value="">All Statuses</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="expired">Expired</option>
                </select>
            </div>
            <div class="form-group-apple" style="margin-bottom: 0;">
                <label for="activationFilter" class="form-label-apple">Activation</label>
                <select class="form-input-apple" id="activationFilter" onchange="filterUUIDs()">
                    <option value="">All UUIDs</option>
                    <option value="true">Activated</option>
                    <option value="false">Not Activated</option>
                </select>
            </div>
            <div class="form-group-apple" style="margin-bottom: 0;">
                <label for="searchInput" class="form-label-apple">Search</label>
                <input
                    type="text"
                    class="form-input-apple"
                    id="searchInput"
                    placeholder="Search UUIDs..."
                    onkeyup="filterUUIDs()"
                >
            </div>
            <div style="display: flex; gap: var(--spacing-sm);">
                <button class="btn-apple btn-apple-secondary" onclick="clearFilters()">
                    <i class="fas fa-times"></i>
                    Clear
                </button>
                <button class="btn-apple btn-apple-ghost" onclick="refreshUUIDs()" title="Refresh">
                    <i class="fas fa-refresh"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- UUIDs Table -->
<div class="card-apple">
    <div class="card-apple-header">
        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: var(--spacing-md);">
            <h3 style="margin: 0; display: flex; align-items: center; gap: var(--spacing-sm);">
                <i class="fas fa-list-ul" style="color: var(--color-primary);"></i>
                UUID Collection
            </h3>
            <div style="display: flex; align-items: center; gap: var(--spacing-sm); color: var(--color-text-secondary); font-size: var(--font-size-sm);">
                <span id="uuidCount">{{ uuids|length }} UUIDs</span>
                <span>•</span>
                <span id="filteredCount" style="display: none;"></span>
            </div>
        </div>
    </div>
    <div class="card-apple-body" style="padding: 0;">
        {% if uuids %}
        <div class="table-apple" id="uuidsTable">
            <thead>
                <tr>
                    <th>UUID</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Expires</th>
                    <th>Usage</th>
                    <th>Activated</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for uuid in uuids %}
                <tr data-uuid="{{ uuid.uuid }}" data-status="{{ uuid.status }}" data-activated="{{ uuid.is_activated|lower }}" class="uuid-row">
                    <td>
                        <div>
                            <code style="background: var(--color-gray-100); padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-family: var(--font-family-mono); color: var(--color-primary); font-size: var(--font-size-sm);">
                                {{ uuid.uuid }}
                            </code>
                            {% if uuid.description %}
                            <div style="margin-top: var(--spacing-xs); color: var(--color-text-secondary); font-size: var(--font-size-sm);">
                                {{ uuid.description }}
                            </div>
                            {% endif %}
                        </div>
                    </td>
                    <td>
                        <div style="display: flex; flex-direction: column; gap: var(--spacing-xs);">
                            <span class="badge-apple badge-apple-{{ 'success' if uuid.status == 'active' else 'danger' if uuid.status == 'expired' else 'secondary' }}">
                                {{ uuid.status }}
                            </span>
                            {% if uuid.is_expired and uuid.status != 'expired' %}
                            <span class="badge-apple badge-apple-danger">
                                <i class="fas fa-clock"></i>
                                Expired
                            </span>
                            {% endif %}
                        </div>
                    </td>
                    <td style="color: var(--color-text-secondary); font-size: var(--font-size-sm);">
                        {{ uuid.created_at.strftime('%Y-%m-%d') }}
                        <div style="font-size: var(--font-size-xs); color: var(--color-text-tertiary);">
                            {{ uuid.created_at.strftime('%H:%M') }}
                        </div>
                    </td>
                    <td style="color: var(--color-text-secondary); font-size: var(--font-size-sm);">
                        {% if uuid.expires_at %}
                        {{ uuid.expires_at.strftime('%Y-%m-%d') }}
                        <div style="font-size: var(--font-size-xs); color: var(--color-text-tertiary);">
                            {{ uuid.expires_at.strftime('%H:%M') }}
                        </div>
                        {% else %}
                        <span style="color: var(--color-text-tertiary);">Never</span>
                        {% endif %}
                    </td>
                    <td>
                        <div style="display: flex; flex-direction: column; gap: var(--spacing-xs);">
                            <span class="badge-apple badge-apple-secondary">{{ uuid.usage_count }}</span>
                            {% if uuid.last_used_at %}
                            <small style="color: var(--color-text-tertiary); font-size: var(--font-size-xs);">
                                Last: {{ uuid.last_used_at.strftime('%m-%d %H:%M') }}
                            </small>
                            {% endif %}
                        </div>
                    </td>
                    <td>
                        {% if uuid.is_activated %}
                        <div style="display: flex; flex-direction: column; gap: var(--spacing-xs);">
                            <span class="badge-apple badge-apple-warning">
                                <i class="fas fa-star"></i>
                                Yes
                            </span>
                            {% if uuid.activated_at %}
                            <small style="color: var(--color-text-tertiary); font-size: var(--font-size-xs);">
                                {{ uuid.activated_at.strftime('%m-%d %H:%M') }}
                            </small>
                            {% endif %}
                            {% if uuid.activation_method %}
                            <small style="color: var(--color-text-tertiary); font-size: var(--font-size-xs);">
                                via {{ uuid.activation_method }}
                            </small>
                            {% endif %}
                        </div>
                        {% else %}
                        <span class="badge-apple badge-apple-secondary">No</span>
                        {% endif %}
                    </td>
                    <td>
                        <div style="display: flex; gap: var(--spacing-xs);">
                            <button class="btn-apple btn-apple-ghost btn-apple-sm" onclick="editUUID('{{ uuid.uuid }}')" title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            {% if not uuid.is_activated and uuid.status == 'active' and not uuid.is_expired %}
                            <button class="btn-apple btn-apple-ghost btn-apple-sm" onclick="activateUUID('{{ uuid.uuid }}')" title="Activate" style="color: var(--color-orange);">
                                <i class="fas fa-star"></i>
                            </button>
                            {% endif %}
                            <button class="btn-apple btn-apple-ghost btn-apple-sm" onclick="testUUID('{{ uuid.uuid }}')" title="Test">
                                <i class="fas fa-vial"></i>
                            </button>
                            <button class="btn-apple btn-apple-ghost btn-apple-sm" onclick="deleteUUID('{{ uuid.uuid }}')" title="Delete" style="color: var(--color-danger);">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </div>
        {% else %}
        <div style="text-align: center; padding: var(--spacing-3xl); color: var(--color-text-secondary);">
            <div style="font-size: 3rem; margin-bottom: var(--spacing-lg); opacity: 0.5;">
                <i class="fas fa-inbox"></i>
            </div>
            <h4 style="color: var(--color-text-secondary); margin-bottom: var(--spacing-sm);">No UUIDs Found</h4>
            <p style="margin-bottom: var(--spacing-lg);">Get started by creating your first UUID</p>
            <button class="btn-apple btn-apple-primary" onclick="createNewUUID()">
                <i class="fas fa-plus"></i>
                Create First UUID
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- Create/Edit UUID Modal -->
<div class="modal fade" id="uuidModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Create New UUID</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uuidForm">
                    <input type="hidden" id="editingUUID" value="">
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="expires_at" class="form-label">Expiration Date</label>
                        <input type="datetime-local" class="form-control" id="expires_at" name="expires_at">
                    </div>
                    <div class="mb-3" id="statusGroup" style="display: none;">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitUUID()">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Test UUID Modal -->
<div class="modal fade" id="testModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Test UUID</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="testResults"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentModal;

function createNewUUID() {
    document.getElementById('modalTitle').textContent = 'Create New UUID';
    document.getElementById('editingUUID').value = '';
    document.getElementById('uuidForm').reset();
    document.getElementById('statusGroup').style.display = 'none';
    
    currentModal = new bootstrap.Modal(document.getElementById('uuidModal'));
    currentModal.show();
}

function editUUID(uuid) {
    document.getElementById('modalTitle').textContent = 'Edit UUID';
    document.getElementById('editingUUID').value = uuid;
    document.getElementById('statusGroup').style.display = 'block';
    
    // Load current UUID data
    loadUUIDData(uuid);
    
    currentModal = new bootstrap.Modal(document.getElementById('uuidModal'));
    currentModal.show();
}

async function loadUUIDData(uuid) {
    try {
        const response = await fetch(`/admin/uuids/${uuid}`, {
            headers: getAuthHeaders()
        });
        
        if (response.ok) {
            const data = await response.json();
            document.getElementById('description').value = data.description || '';
            document.getElementById('status').value = data.status;
            
            if (data.expires_at) {
                // Convert to local datetime format
                const date = new Date(data.expires_at);
                document.getElementById('expires_at').value = date.toISOString().slice(0, 16);
            }
        }
    } catch (error) {
        console.error('Error loading UUID data:', error);
    }
}

async function submitUUID() {
    const form = document.getElementById('uuidForm');
    const formData = new FormData(form);
    const editingUUID = document.getElementById('editingUUID').value;
    
    const data = {
        description: formData.get('description') || null,
        expires_at: formData.get('expires_at') || null
    };
    
    if (editingUUID) {
        data.status = formData.get('status');
    }
    
    try {
        const url = editingUUID ? `/admin/uuids/${editingUUID}` : '/admin/uuids';
        const method = editingUUID ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                ...getAuthHeaders()
            },
            body: JSON.stringify(data)
        });
        
        if (response.ok) {
            const result = await response.json();
            alert(`UUID ${editingUUID ? 'updated' : 'created'} successfully!`);
            currentModal.hide();
            location.reload();
        } else {
            const error = await response.json();
            alert(`Error: ${error.detail}`);
        }
    } catch (error) {
        alert(`Error: ${error.message}`);
    }
}

async function deleteUUID(uuid) {
    if (!confirm(`Are you sure you want to delete UUID ${uuid}?`)) {
        return;
    }
    
    try {
        const response = await fetch(`/admin/uuids/${uuid}`, {
            method: 'DELETE',
            headers: getAuthHeaders()
        });
        
        if (response.ok) {
            alert('UUID deleted successfully!');
            location.reload();
        } else {
            const error = await response.json();
            alert(`Error: ${error.detail}`);
        }
    } catch (error) {
        alert(`Error: ${error.message}`);
    }
}

async function testUUID(uuid) {
    const resultsDiv = document.getElementById('testResults');
    resultsDiv.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Testing...</div>';
    
    const testModal = new bootstrap.Modal(document.getElementById('testModal'));
    testModal.show();
    
    try {
        // Test validation endpoint
        const validateResponse = await fetch(`/validate/${uuid}`);
        const validateData = await validateResponse.json();
        
        // Test status endpoint
        const statusResponse = await fetch(`/status/${uuid}`);
        const statusData = await statusResponse.json();
        
        resultsDiv.innerHTML = `
            <h6>Validation Test:</h6>
            <div class="alert alert-${validateResponse.ok ? 'success' : 'danger'}">
                <strong>Status:</strong> ${validateResponse.status}<br>
                <strong>Message:</strong> ${validateData.message || validateData.detail}<br>
                <strong>Valid:</strong> ${validateData.is_valid}<br>
                <strong>Activated:</strong> ${validateData.is_activated}<br>
                <strong>Activated Now:</strong> ${validateData.activated_now}
            </div>
            
            <h6>Status Check:</h6>
            <div class="alert alert-info">
                <strong>Status:</strong> ${statusData.status}<br>
                <strong>Usage Count:</strong> ${statusData.usage_count}<br>
                <strong>Valid:</strong> ${statusData.is_valid}<br>
                <strong>Activated:</strong> ${statusData.is_activated}<br>
                <strong>Expired:</strong> ${statusData.is_expired}
            </div>
        `;
    } catch (error) {
        resultsDiv.innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
    }
}

function filterUUIDs() {
    const statusFilter = document.getElementById('statusFilter').value;
    const activationFilter = document.getElementById('activationFilter').value;
    const searchInput = document.getElementById('searchInput').value.toLowerCase();
    
    const rows = document.querySelectorAll('#uuidsTable tbody tr');
    
    rows.forEach(row => {
        const uuid = row.dataset.uuid.toLowerCase();
        const status = row.dataset.status;
        const activated = row.dataset.activated;
        
        let show = true;
        
        if (statusFilter && status !== statusFilter) {
            show = false;
        }
        
        if (activationFilter && activated !== activationFilter) {
            show = false;
        }
        
        if (searchInput && !uuid.includes(searchInput)) {
            show = false;
        }
        
        row.style.display = show ? '' : 'none';
    });
}

function clearFilters() {
    document.getElementById('statusFilter').value = '';
    document.getElementById('activationFilter').value = '';
    document.getElementById('searchInput').value = '';
    filterUUIDs();
}
</script>
{% endblock %}
