{% extends "base.html" %}

{% block title %}Manage UUIDs - UUID Management System{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: var(--spacing-md);">
        <div>
            <h1 class="page-title">Manage UUIDs</h1>
            <p class="page-subtitle">Create, edit, and monitor your UUID collection</p>
        </div>
        <button class="btn-apple btn-apple-primary btn-apple-lg" onclick="createNewUUID()">
            <i class="fas fa-plus"></i>
            Create New UUID
        </button>
    </div>
</div>

<!-- Filters -->
<div class="card-apple" style="margin-bottom: var(--spacing-2xl);">
    <div class="card-apple-header">
        <h3 style="margin: 0; display: flex; align-items: center; gap: var(--spacing-sm);">
            <i class="fas fa-filter" style="color: var(--color-primary);"></i>
            Filters
        </h3>
    </div>
    <div class="card-apple-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-md); align-items: end;">
            <div class="form-group-apple" style="margin-bottom: 0;">
                <label for="statusFilter" class="form-label-apple">Status</label>
                <select class="form-input-apple" id="statusFilter" onchange="filterUUIDs()">
                    <option value="">All Statuses</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="expired">Expired</option>
                </select>
            </div>
            <div class="form-group-apple" style="margin-bottom: 0;">
                <label for="activationFilter" class="form-label-apple">Activation</label>
                <select class="form-input-apple" id="activationFilter" onchange="filterUUIDs()">
                    <option value="">All UUIDs</option>
                    <option value="true">Activated</option>
                    <option value="false">Not Activated</option>
                </select>
            </div>
            <div class="form-group-apple" style="margin-bottom: 0;">
                <label for="searchInput" class="form-label-apple">Search</label>
                <input
                    type="text"
                    class="form-input-apple"
                    id="searchInput"
                    placeholder="Search UUIDs..."
                    onkeyup="filterUUIDs()"
                >
            </div>
            <div style="display: flex; gap: var(--spacing-sm);">
                <button class="btn-apple btn-apple-secondary" onclick="clearFilters()">
                    <i class="fas fa-times"></i>
                    Clear
                </button>
                <button class="btn-apple btn-apple-ghost" onclick="refreshUUIDs()" title="Refresh">
                    <i class="fas fa-refresh"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- UUIDs Table -->
<div class="card-apple">
    <div class="card-apple-header">
        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: var(--spacing-md);">
            <h3 style="margin: 0; display: flex; align-items: center; gap: var(--spacing-sm);">
                <i class="fas fa-list-ul" style="color: var(--color-primary);"></i>
                UUID Collection
            </h3>
            <div style="display: flex; align-items: center; gap: var(--spacing-sm); color: var(--color-text-secondary); font-size: var(--font-size-sm);">
                <span id="uuidCount">{{ uuids|length }} UUIDs</span>
                <span>•</span>
                <span id="filteredCount" style="display: none;"></span>
            </div>
        </div>
    </div>
    <div class="card-apple-body">
        {% if uuids %}
        <div id="uuidsContainer" class="fade-in-up">
            {% for uuid in uuids %}
            <div class="uuid-card-row" data-uuid="{{ uuid.uuid }}" data-status="{{ uuid.status }}" data-activated="{{ uuid.is_activated|lower }}">
                <div class="uuid-card-header">
                    <div class="uuid-card-main">
                        <!-- UUID Display with Copy Functionality -->
                        <div class="uuid-display" onclick="toggleUUIDExpansion(this)" title="Click to expand/collapse">
                            <span class="uuid-text">{{ uuid.uuid }}</span>
                            <i class="fas fa-chevron-down expand-icon"></i>
                            <i class="fas fa-copy copy-icon" onclick="copyUUID(event, '{{ uuid.uuid }}')" title="Copy UUID"></i>
                        </div>

                        {% if uuid.description %}
                        <div style="margin-top: var(--spacing-sm); color: var(--color-text-secondary); font-size: var(--font-size-sm); font-weight: var(--font-weight-medium);">
                            {{ uuid.description }}
                        </div>
                        {% endif %}

                        <!-- Status Badges -->
                        <div class="status-badge-group" style="margin-top: var(--spacing-sm);">
                            <span class="status-badge-{{ 'success' if uuid.status == 'active' else 'danger' if uuid.status == 'expired' else 'secondary' }}">
                                <i class="fas fa-{{ 'check-circle' if uuid.status == 'active' else 'times-circle' if uuid.status == 'expired' else 'pause-circle' }}"></i>
                                {{ uuid.status.title() }}
                            </span>

                            {% if uuid.is_expired and uuid.status != 'expired' %}
                            <span class="status-badge-danger">
                                <i class="fas fa-clock"></i>
                                Expired
                            </span>
                            {% endif %}

                            {% if uuid.is_activated %}
                            <span class="status-badge-warning">
                                <i class="fas fa-star"></i>
                                Activated
                            </span>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-button-group">
                        <button class="action-btn" onclick="editUUID('{{ uuid.uuid }}')" title="Edit UUID">
                            <i class="fas fa-edit"></i>
                        </button>

                        {% if not uuid.is_activated and uuid.status == 'active' and not uuid.is_expired %}
                        <button class="action-btn success" onclick="activateUUID('{{ uuid.uuid }}')" title="Activate UUID">
                            <i class="fas fa-star"></i>
                        </button>
                        {% endif %}

                        <button class="action-btn primary" onclick="testUUID('{{ uuid.uuid }}')" title="Test UUID">
                            <i class="fas fa-vial"></i>
                        </button>

                        <button class="action-btn danger" onclick="deleteUUID('{{ uuid.uuid }}')" title="Delete UUID">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>

                <!-- UUID Metadata -->
                <div class="uuid-card-meta">
                    <div class="uuid-meta-item">
                        <div class="uuid-meta-label">Created</div>
                        <div class="uuid-meta-value">
                            {{ uuid.created_at.strftime('%Y-%m-%d') }}
                            <div style="font-size: var(--font-size-xs); color: var(--color-text-tertiary); margin-top: 2px;">
                                {{ uuid.created_at.strftime('%H:%M') }}
                            </div>
                        </div>
                    </div>

                    <div class="uuid-meta-item">
                        <div class="uuid-meta-label">Expires</div>
                        <div class="uuid-meta-value">
                            {% if uuid.expires_at %}
                            {{ uuid.expires_at.strftime('%Y-%m-%d') }}
                            <div style="font-size: var(--font-size-xs); color: var(--color-text-tertiary); margin-top: 2px;">
                                {{ uuid.expires_at.strftime('%H:%M') }}
                            </div>
                            {% else %}
                            <span style="color: var(--color-text-tertiary);">Never</span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="uuid-meta-item">
                        <div class="uuid-meta-label">Usage Count</div>
                        <div class="uuid-meta-value">
                            <span class="status-badge-secondary">{{ uuid.usage_count }}</span>
                            {% if uuid.last_used_at %}
                            <div style="font-size: var(--font-size-xs); color: var(--color-text-tertiary); margin-top: 4px;">
                                Last: {{ uuid.last_used_at.strftime('%m-%d %H:%M') }}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    {% if uuid.is_activated %}
                    <div class="uuid-meta-item">
                        <div class="uuid-meta-label">Activation</div>
                        <div class="uuid-meta-value">
                            {% if uuid.activated_at %}
                            {{ uuid.activated_at.strftime('%m-%d %H:%M') }}
                            {% endif %}
                            {% if uuid.activation_method %}
                            <div style="font-size: var(--font-size-xs); color: var(--color-text-tertiary); margin-top: 2px;">
                                via {{ uuid.activation_method }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="empty-state">
            <div class="empty-state-icon">
                <i class="fas fa-layer-group"></i>
            </div>
            <h3 class="empty-state-title">No UUIDs Found</h3>
            <p class="empty-state-description">
                Your UUID collection is empty. Create your first UUID to get started with managing unique identifiers for your applications.
            </p>
            <button class="btn-apple btn-apple-primary" onclick="createNewUUID()">
                <i class="fas fa-plus"></i>
                Create First UUID
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- Create/Edit UUID Modal -->
<div class="modal fade" id="uuidModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Create New UUID</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uuidForm">
                    <input type="hidden" id="editingUUID" value="">
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="expires_at" class="form-label">Expiration Date</label>
                        <input type="datetime-local" class="form-control" id="expires_at" name="expires_at">
                    </div>
                    <div class="mb-3" id="statusGroup" style="display: none;">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitUUID()">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Test UUID Modal -->
<div class="modal fade" id="testModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Test UUID</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="testResults"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentModal;

// Enhanced UUID Display Functions
function toggleUUIDExpansion(element) {
    element.classList.toggle('expanded');

    // Add animation class
    element.classList.add('fade-in-up');
    setTimeout(() => {
        element.classList.remove('fade-in-up');
    }, 400);
}

function copyUUID(event, uuid) {
    event.stopPropagation(); // Prevent expansion toggle

    navigator.clipboard.writeText(uuid).then(() => {
        const copyIcon = event.target;
        const originalClass = copyIcon.className;

        // Show success feedback
        copyIcon.className = 'fas fa-check';
        copyIcon.style.color = 'var(--color-success)';

        // Add success animation to parent
        const uuidDisplay = copyIcon.closest('.uuid-display');
        uuidDisplay.classList.add('uuid-copy-success');

        // Show notification
        showNotification(`UUID copied to clipboard: ${uuid.substring(0, 8)}...`, 'success');

        // Reset after delay
        setTimeout(() => {
            copyIcon.className = originalClass;
            copyIcon.style.color = '';
            uuidDisplay.classList.remove('uuid-copy-success');
        }, 1000);
    }).catch(err => {
        showNotification('Failed to copy UUID', 'error');
        console.error('Failed to copy: ', err);
    });
}

function createNewUUID() {
    document.getElementById('modalTitle').textContent = 'Create New UUID';
    document.getElementById('editingUUID').value = '';
    document.getElementById('uuidForm').reset();
    document.getElementById('statusGroup').style.display = 'none';

    currentModal = new bootstrap.Modal(document.getElementById('uuidModal'));
    currentModal.show();
}

function editUUID(uuid) {
    document.getElementById('modalTitle').textContent = 'Edit UUID';
    document.getElementById('editingUUID').value = uuid;
    document.getElementById('statusGroup').style.display = 'block';

    // Load current UUID data
    loadUUIDData(uuid);

    currentModal = new bootstrap.Modal(document.getElementById('uuidModal'));
    currentModal.show();
}

async function loadUUIDData(uuid) {
    try {
        const response = await fetch(`/admin/uuids/${uuid}`, {
            headers: getAuthHeaders()
        });
        
        if (response.ok) {
            const data = await response.json();
            document.getElementById('description').value = data.description || '';
            document.getElementById('status').value = data.status;
            
            if (data.expires_at) {
                // Convert to local datetime format
                const date = new Date(data.expires_at);
                document.getElementById('expires_at').value = date.toISOString().slice(0, 16);
            }
        }
    } catch (error) {
        console.error('Error loading UUID data:', error);
    }
}

async function submitUUID() {
    const form = document.getElementById('uuidForm');
    const formData = new FormData(form);
    const editingUUID = document.getElementById('editingUUID').value;
    
    const data = {
        description: formData.get('description') || null,
        expires_at: formData.get('expires_at') || null
    };
    
    if (editingUUID) {
        data.status = formData.get('status');
    }
    
    try {
        const url = editingUUID ? `/admin/uuids/${editingUUID}` : '/admin/uuids';
        const method = editingUUID ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                ...getAuthHeaders()
            },
            body: JSON.stringify(data)
        });
        
        if (response.ok) {
            const result = await response.json();
            alert(`UUID ${editingUUID ? 'updated' : 'created'} successfully!`);
            currentModal.hide();
            location.reload();
        } else {
            const error = await response.json();
            alert(`Error: ${error.detail}`);
        }
    } catch (error) {
        alert(`Error: ${error.message}`);
    }
}

async function deleteUUID(uuid) {
    if (!confirm('Are you sure you want to delete this UUID? This action cannot be undone.')) {
        return;
    }

    const button = event.target.closest('.action-btn');
    const uuidCard = button.closest('.uuid-card-row');
    const originalContent = button.innerHTML;

    // Show loading state
    button.disabled = true;
    button.innerHTML = '<div class="loading-spinner"></div>';

    try {
        const response = await fetch(`/admin/uuids/${uuid}`, {
            method: 'DELETE',
            headers: getAuthHeaders()
        });

        if (response.ok) {
            // Animate card removal
            uuidCard.style.transform = 'translateX(-100%)';
            uuidCard.style.opacity = '0';

            showNotification(`UUID deleted successfully: ${uuid.substring(0, 8)}...`, 'success');

            // Remove from DOM after animation
            setTimeout(() => {
                uuidCard.remove();

                // Check if no UUIDs left
                const container = document.getElementById('uuidsContainer');
                if (container && container.children.length === 0) {
                    location.reload();
                }
            }, 300);
        } else {
            const error = await response.json();
            showNotification(`Delete failed: ${error.detail}`, 'error');

            // Reset button
            button.disabled = false;
            button.innerHTML = originalContent;
        }
    } catch (error) {
        showNotification(`Delete error: ${error.message}`, 'error');

        // Reset button
        button.disabled = false;
        button.innerHTML = originalContent;
    }
}

async function testUUID(uuid) {
    const button = event.target.closest('.action-btn');
    const originalContent = button.innerHTML;

    // Show loading state
    button.disabled = true;
    button.innerHTML = '<div class="loading-spinner"></div>';

    try {
        const response = await fetch(`/validate/${uuid}`, {
            method: 'GET',
            headers: getAuthHeaders()
        });

        if (response.ok) {
            const result = await response.json();

            // Show success state
            button.innerHTML = '<i class="fas fa-check"></i>';
            button.style.background = 'var(--color-success)';
            button.style.color = 'var(--color-white)';

            showNotification(`UUID test successful: ${result.message}`, 'success');

            // Reset button after delay
            setTimeout(() => {
                button.disabled = false;
                button.innerHTML = originalContent;
                button.style.background = '';
                button.style.color = '';
            }, 2000);
        } else {
            const error = await response.json();

            // Show error state
            button.innerHTML = '<i class="fas fa-times"></i>';
            button.style.background = 'var(--color-danger)';
            button.style.color = 'var(--color-white)';

            showNotification(`UUID test failed: ${error.detail}`, 'error');

            // Reset button after delay
            setTimeout(() => {
                button.disabled = false;
                button.innerHTML = originalContent;
                button.style.background = '';
                button.style.color = '';
            }, 2000);
        }
    } catch (error) {
        showNotification(`Test error: ${error.message}`, 'error');

        // Reset button
        button.disabled = false;
        button.innerHTML = originalContent;
    }
}

async function activateUUID(uuid) {
    const button = event.target.closest('.action-btn');
    const originalContent = button.innerHTML;

    // Show loading state
    button.disabled = true;
    button.innerHTML = '<div class="loading-spinner"></div>';

    try {
        const response = await fetch(`/admin/uuids/${uuid}/activate`, {
            method: 'POST',
            headers: getAuthHeaders()
        });

        if (response.ok) {
            const result = await response.json();

            // Show success state
            button.innerHTML = '<i class="fas fa-star"></i>';
            button.style.background = 'var(--color-orange)';
            button.style.color = 'var(--color-white)';

            showNotification(`UUID activated successfully: ${result.message}`, 'success');

            // Update the card to show activated state
            setTimeout(() => {
                location.reload(); // Reload to show updated state
            }, 1000);
        } else {
            const error = await response.json();
            showNotification(`Activation failed: ${error.detail}`, 'error');

            // Reset button
            button.disabled = false;
            button.innerHTML = originalContent;
        }
    } catch (error) {
        showNotification(`Activation error: ${error.message}`, 'error');

        // Reset button
        button.disabled = false;
        button.innerHTML = originalContent;
    }
}

function filterUUIDs() {
    const statusFilter = document.getElementById('statusFilter').value;
    const activationFilter = document.getElementById('activationFilter').value;
    const searchInput = document.getElementById('searchInput').value.toLowerCase();
    
    const rows = document.querySelectorAll('#uuidsTable tbody tr');
    
    rows.forEach(row => {
        const uuid = row.dataset.uuid.toLowerCase();
        const status = row.dataset.status;
        const activated = row.dataset.activated;
        
        let show = true;
        
        if (statusFilter && status !== statusFilter) {
            show = false;
        }
        
        if (activationFilter && activated !== activationFilter) {
            show = false;
        }
        
        if (searchInput && !uuid.includes(searchInput)) {
            show = false;
        }
        
        row.style.display = show ? '' : 'none';
    });
}

function clearFilters() {
    document.getElementById('statusFilter').value = '';
    document.getElementById('activationFilter').value = '';
    document.getElementById('searchInput').value = '';
    filterUUIDs();
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: var(--spacing-lg);
        right: var(--spacing-lg);
        background: ${type === 'success' ? 'var(--color-success)' : type === 'error' ? 'var(--color-danger)' : 'var(--color-primary)'};
        color: var(--color-white);
        padding: var(--spacing-md) var(--spacing-lg);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform var(--transition-base);
        max-width: 400px;
        font-weight: var(--font-weight-medium);
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after delay
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

function getAuthHeaders() {
    const token = localStorage.getItem('access_token');
    return token ? {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    } : {
        'Content-Type': 'application/json'
    };
}

// Initialize animations on page load
document.addEventListener('DOMContentLoaded', function() {
    // Add staggered animation to UUID cards
    const cards = document.querySelectorAll('.uuid-card-row');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in-up');
    });

    // Add search highlighting functionality
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            highlightSearchResults(this.value);
        });
    }
});

function highlightSearchResults(searchTerm) {
    const uuidDisplays = document.querySelectorAll('.uuid-display .uuid-text');

    uuidDisplays.forEach(display => {
        const originalText = display.textContent;

        if (searchTerm && originalText.toLowerCase().includes(searchTerm.toLowerCase())) {
            const regex = new RegExp(`(${searchTerm})`, 'gi');
            const highlightedText = originalText.replace(regex, '<span class="search-highlight">$1</span>');
            display.innerHTML = highlightedText;
        } else {
            display.textContent = originalText;
        }
    });
}
</script>
{% endblock %}
